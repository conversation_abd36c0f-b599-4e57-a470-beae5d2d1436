import React, { useState, useMemo, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, FlatList, Switch, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n, { changeLanguage } from '../i18n';
import { updateSetting, getSetting } from '../constants/Storage';
import { useSettings } from '../context/SettingsContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface CurrencyOption {
    symbol: string;
}

interface LanguageOption {
    code: string;
    name: string;
}

const Settings = () => {
    const [showCurrencySelector, setShowCurrencySelector] = useState(false);
    const [showLanguageSelector, setShowLanguageSelector] = useState(false);
    const [personalNote, setPersonalNote] = useState('');
    const { currency, language, hideBudgetModule, hideMemberSection, hideExcludeFromBudget, hideShoppingPlatformSection, refreshSettings } = useSettings();

    const currencies: CurrencyOption[] = [
        { symbol: '¥' },
        { symbol: '$' },
        { symbol: '€' },
        { symbol: '£' },
        { symbol: '₩' },
        { symbol: '₹' },
        { symbol: '₽' },
        { symbol: 'HK$' },
        { symbol: 'A$' },
        { symbol: 'C$' },
        { symbol: '฿' },
        { symbol: '₺' },
    ];

    const languages: LanguageOption[] = useMemo(() => [
        { code: 'zh', name: i18n.t('settings.languageOptions.zh') },
        { code: 'en', name: i18n.t('settings.languageOptions.en') },
    ], [language]); // 依赖于 language 变化重新计算

    const handleCurrencyChange = async (newCurrency: string) => {
        try {
            await updateSetting('currency', newCurrency);
            refreshSettings();
            setShowCurrencySelector(false);
            Alert.alert(
                i18n.t('settings.success'),
                i18n.t('settings.currencyUpdated'),
                [{ text: 'OK', onPress: () => router.replace('/') }]
            );
        } catch (error) {
            console.error('Failed to update currency:', error);
            Alert.alert(i18n.t('settings.error'), i18n.t('settings.updateFailed'));
        }
    };

    const handleLanguageChange = async (newLanguage: string) => {
        try {
            await updateSetting('language', newLanguage);
            changeLanguage(newLanguage);
            refreshSettings();
            setShowLanguageSelector(false);
            Alert.alert(
                i18n.t('settings.success'),
                i18n.t('settings.currencyUpdated'), // 复用这个翻译
                [{ text: 'OK', onPress: () => router.replace('/') }]
            );
        } catch (error) {
            console.error('Failed to update language:', error);
            Alert.alert(i18n.t('settings.error'), i18n.t('settings.updateFailed'));
        }
    };

    const handleHideBudgetModuleChange = async (value: boolean) => {
        try {
            await updateSetting('hideBudgetModule', value.toString());
            refreshSettings();
        } catch (error) {
            console.error('Failed to update hideBudgetModule setting:', error);
            Alert.alert(i18n.t('settings.error'), i18n.t('settings.updateFailed'));
        }
    };

    const handleHideMemberSectionChange = async (value: boolean) => {
        try {
            await updateSetting('hideMemberSection', value.toString());
            refreshSettings();
        } catch (error) {
            console.error('Failed to update hideMemberSection setting:', error);
            Alert.alert(i18n.t('settings.error'), i18n.t('settings.updateFailed'));
        }
    };

    const handleHideExcludeFromBudgetChange = async (value: boolean) => {
        try {
            await updateSetting('hideExcludeFromBudget', value.toString());
            refreshSettings();
        } catch (error) {
            console.error('Failed to update hideExcludeFromBudget setting:', error);
            Alert.alert(i18n.t('settings.error'), i18n.t('settings.updateFailed'));
        }
    };

    const handleHideShoppingPlatformSectionChange = async (value: boolean) => {
        try {
            await updateSetting('hideShoppingPlatformSection', value.toString());
            refreshSettings();
        } catch (error) {
            console.error('Failed to update hideShoppingPlatformSection setting:', error);
            Alert.alert(i18n.t('settings.error'), i18n.t('settings.updateFailed'));
        }
    };

    const handlePersonalNoteChange = (value: string) => {
        setPersonalNote(value);
    };

    const handlePersonalNoteBlur = async () => {
        try {
            await updateSetting('personalNote', personalNote);
            // 清除今天的显示记录，这样修改后的留言会立即显示
            await AsyncStorage.removeItem('personalNoteLastShown');
        } catch (error) {
            console.error('Failed to update personal note:', error);
            Alert.alert(i18n.t('settings.error'), i18n.t('settings.updateFailed'));
        }
    };

    // 加载个人留言
    useEffect(() => {
        const loadPersonalNote = async () => {
            try {
                const note = await getSetting('personalNote');
                if (note) {
                    setPersonalNote(note);
                } else {
                    // 如果没有设置过个人留言，设置默认值
                    const defaultNote = i18n.t('settings.personalNoteDefault');
                    setPersonalNote(defaultNote);
                    await updateSetting('personalNote', defaultNote);
                }
            } catch (error) {
                console.error('Failed to load personal note:', error);
            }
        };
        loadPersonalNote();
    }, []);

    const handlePrepaidCardsPress = () => {
        // TODO: Implement actual premium status check
        // For now, assume user is not premium and show upgrade prompt
        const isPremium = false; // This should be replaced with actual premium check

        if (!isPremium) {
            Alert.alert(
                i18n.t('settings.premiumRequired'),
                i18n.t('prepaidCards.premiumRequired'),
                [
                    { text: i18n.t('common.cancel'), style: 'cancel' },
                    {
                        text: i18n.t('settings.upgrade'),
                        onPress: () => {
                            // TODO: Navigate to premium upgrade screen
                            console.log('Navigate to premium upgrade');
                        }
                    }
                ]
            );
            return;
        }

        router.push('/screens/prepaidCards');
    };

    const handleShoppingPlatformsPress = () => {
        router.push('/screens/shoppingPlatforms');
    };

    const renderCurrencyItem = ({ item }: { item: CurrencyOption }) => (
        <TouchableOpacity
            style={[
                styles.currencyItem,
                currency === item.symbol && styles.selectedCurrency
            ]}
            onPress={() => handleCurrencyChange(item.symbol)}
        >
            <Text style={[
                styles.currencySymbol,
                currency === item.symbol && styles.selectedCurrencyText
            ]}>
                {item.symbol}
            </Text>
            {currency === item.symbol && (
                <View style={styles.checkmarkBadge}>
                    <Ionicons name="checkmark" size={12} color="white" />
                </View>
            )}
        </TouchableOpacity>
    );

    const renderLanguageItem = ({ item }: { item: LanguageOption }) => (
        <TouchableOpacity
            style={[
                styles.languageItem,
                language === item.code && styles.selectedLanguage
            ]}
            onPress={() => handleLanguageChange(item.code)}
        >
            <Text style={[
                styles.languageText,
                language === item.code && styles.selectedLanguageText
            ]}>
                {item.name}
            </Text>
            {language === item.code && (
                <Ionicons name="checkmark" size={20} color="#dc4446" />
            )}
        </TouchableOpacity>
    );

    return (
        <ScrollView style={styles.container}>
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>{i18n.t('settings.general')}</Text>

                <TouchableOpacity
                    style={styles.settingItem}
                    onPress={() => setShowCurrencySelector(!showCurrencySelector)}
                >
                    <View style={styles.settingLeft}>
                        <Ionicons name="cash-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.currency')}</Text>
                    </View>
                    <View style={styles.settingRight}>
                        <Text style={styles.settingValue}>{currency}</Text>
                        <Ionicons
                            name={showCurrencySelector ? "chevron-down" : "chevron-forward"}
                            size={20}
                            color="#ccc"
                        />
                    </View>
                </TouchableOpacity>

                {showCurrencySelector && (
                    <View style={styles.currencyGrid}>
                        <FlatList
                            data={currencies}
                            renderItem={renderCurrencyItem}
                            keyExtractor={(item) => item.symbol}
                            numColumns={4}
                            scrollEnabled={false}
                        />
                    </View>
                )}

                <TouchableOpacity
                    style={styles.settingItem}
                    onPress={() => setShowLanguageSelector(!showLanguageSelector)}
                >
                    <View style={styles.settingLeft}>
                        <Ionicons name="language-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.language')}</Text>
                    </View>
                    <View style={styles.settingRight}>
                        <Text style={styles.settingValue}>
                            {languages.find(lang => lang.code === language)?.name || language}
                        </Text>
                        <Ionicons
                            name={showLanguageSelector ? "chevron-down" : "chevron-forward"}
                            size={20}
                            color="#ccc"
                        />
                    </View>
                </TouchableOpacity>

                {showLanguageSelector && (
                    <View style={styles.languageList}>
                        <FlatList
                            data={languages}
                            renderItem={renderLanguageItem}
                            keyExtractor={(item) => item.code}
                            scrollEnabled={false}
                        />
                    </View>
                )}
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>{i18n.t('settings.display')}</Text>

                <View style={styles.settingItem}>
                    <View style={styles.settingLeft}>
                        <Ionicons name="eye-off-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.hideBudgetModule')}</Text>
                    </View>
                    <Switch
                        value={hideBudgetModule}
                        onValueChange={handleHideBudgetModuleChange}
                        trackColor={{ false: '#ccc', true: '#dc4446' }}
                        thumbColor={hideBudgetModule ? '#fff' : '#f4f3f4'}
                    />
                </View>

                <View style={styles.settingItem}>
                    <View style={styles.settingLeft}>
                        <Ionicons name="people-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.hideMemberSection')}</Text>
                    </View>
                    <Switch
                        value={hideMemberSection}
                        onValueChange={handleHideMemberSectionChange}
                        trackColor={{ false: '#ccc', true: '#dc4446' }}
                        thumbColor={hideMemberSection ? '#fff' : '#f4f3f4'}
                    />
                </View>

                <View style={styles.settingItem}>
                    <View style={styles.settingLeft}>
                        <Ionicons name="calculator-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.hideExcludeFromBudget')}</Text>
                    </View>
                    <Switch
                        value={hideExcludeFromBudget}
                        onValueChange={handleHideExcludeFromBudgetChange}
                        trackColor={{ false: '#ccc', true: '#dc4446' }}
                        thumbColor={hideExcludeFromBudget ? '#fff' : '#f4f3f4'}
                    />
                </View>

                <View style={styles.settingItem}>
                    <View style={styles.settingLeft}>
                        <Ionicons name="storefront-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.hideShoppingPlatformSection')}</Text>
                    </View>
                    <Switch
                        value={hideShoppingPlatformSection}
                        onValueChange={handleHideShoppingPlatformSectionChange}
                        trackColor={{ false: '#ccc', true: '#dc4446' }}
                        thumbColor={hideShoppingPlatformSection ? '#fff' : '#f4f3f4'}
                    />
                </View>

                {/* 个人留言设置 */}
                <View style={styles.settingItem}>
                    <View style={styles.settingLeft}>
                        <Ionicons name="chatbubble-outline" size={24} color="#666" />
                        <View style={styles.settingTextContainer}>
                            <Text style={styles.settingText}>{i18n.t('settings.personalNote')}</Text>
                            <Text style={styles.settingDescription}>{i18n.t('settings.personalNoteDescription')}</Text>
                        </View>
                    </View>
                </View>

                <TextInput
                    style={styles.personalNoteInput}
                    placeholder={i18n.t('settings.personalNotePlaceholder')}
                    value={personalNote}
                    onChangeText={handlePersonalNoteChange}
                    onBlur={handlePersonalNoteBlur}
                    maxLength={100}
                    multiline
                    textAlignVertical="top"
                    scrollEnabled={false}
                    autoCorrect={false}
                />
            </View>

            {/* <View style={styles.section}>
                <Text style={styles.sectionTitle}>{i18n.t('settings.features')}</Text>

                <TouchableOpacity
                    style={styles.settingItem}
                    onPress={handlePrepaidCardsPress}
                >
                    <View style={styles.settingLeft}>
                        <Ionicons name="card-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.prepaidCards')}</Text>
                    </View>
                    <Ionicons name="chevron-forward" size={20} color="#ccc" />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.settingItem}
                    onPress={handleShoppingPlatformsPress}
                >
                    <View style={styles.settingLeft}>
                        <Ionicons name="storefront-outline" size={24} color="#666" />
                        <Text style={styles.settingText}>{i18n.t('settings.shoppingPlatforms')}</Text>
                    </View>
                    <Ionicons name="chevron-forward" size={20} color="#ccc" />
                </TouchableOpacity>
            </View> */}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    section: {
        backgroundColor: '#fff',
        borderRadius: 16,
        padding: 16,
        margin: 16,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 16,
        color: '#333',
    },
    settingItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    settingLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    settingText: {
        fontSize: 16,
        color: '#333',
    },
    settingRight: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    settingValue: {
        fontSize: 16,
        color: '#666',
    },
    currencyGrid: {
        marginTop: 12,
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    currencyItem: {
        flex: 1,
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        margin: 6,
        borderRadius: 8,
        position: 'relative',
        borderWidth: 1,
        borderColor: 'transparent',
        backgroundColor: '#f5f5f5',
    },
    selectedCurrency: {
        backgroundColor: '#fff1f1',
        borderColor: '#dc4446',
    },
    currencySymbol: {
        fontSize: 20,
        fontWeight: '600',
        color: '#333',
    },
    selectedCurrencyText: {
        color: '#dc4446',
    },
    checkmarkBadge: {
        position: 'absolute',
        top: 4,
        right: 4,
        borderRadius: 10,
        width: 20,
        height: 20,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#dc4446',
    },
    languageList: {
        marginTop: 12,
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    languageItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginVertical: 2,
    },
    selectedLanguage: {
        backgroundColor: '#fff1f1',
    },
    languageText: {
        fontSize: 16,
        color: '#333',
    },
    selectedLanguageText: {
        color: '#dc4446',
        fontWeight: '500',
    },
    settingTextContainer: {
        flex: 1,
    },
    settingDescription: {
        fontSize: 12,
        color: '#999',
        marginTop: 2,
    },
    personalNoteInput: {
        backgroundColor: '#f9f9f9',
        borderRadius: 8,
        padding: 12,
        marginHorizontal: 16,
        marginTop: 8,
        marginBottom: 16,
        fontSize: 16,
        color: '#333',
        minHeight: 80,
        borderWidth: 1,
        borderColor: '#eee',
    },
});

export default Settings; 